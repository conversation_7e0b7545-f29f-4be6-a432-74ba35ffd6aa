import { useState, useEffect, useMemo, useCallback } from 'react';
import { trailsLogic } from '../simConfig/trailsLogic';
import { SimpleSimulationInputs } from '@/types/simpleTypes';

// Define calculation variable types
export interface CalculationVariable {
  id: string;
  name: string;
  value: number;
  unit: string; // '$', '%', or ''
  description: string;
}

// Define calculation formula type
export interface CalculationFormula {
  id: string;
  name: string;
  formula: string;
  description: string;
}

// Define income type
export type IncomeType = 'directEffort' | 'recurringPac' | 'trailIncome' | 'override' | 'agencyOverride' | 'agencyRecurringPac' | 'agencyTrail';

// Define card text properties
export interface CardTextProperties {
  title: string;
  subtitle: string;
  description?: string;
}

// Define calculation set
export interface CalculationSet {
  variables: CalculationVariable[];
  formula: CalculationFormula;
  result: number;
  textProperties: CardTextProperties;
}

// Define all calculations
export interface Calculations {
  directEffort: CalculationSet;
  recurringPac: CalculationSet;
  trailIncome: CalculationSet;
  override: CalculationSet;
  agencyOverride: CalculationSet;
  agencyRecurringPac: CalculationSet;
  agencyTrail: CalculationSet;
}

// Default calculations
const defaultCalculations: Calculations = {
  directEffort: {
    variables: [
      {
        id: 'initialInvestment',
        name: 'Initial Investment',
        value: 25000,
        unit: '$',
        description: 'Average initial investment per client'
      },
      {
        id: 'numCases',
        name: 'Number of Cases',
        value: 5,
        unit: '',
        description: 'Number of cases closed this month'
      },
      {
        id: 'cdrRate',
        name: 'CDR Rate',
        value: 5.5,
        unit: '%',
        description: 'Commission, Dealer, Representative rate'
      },
      {
        id: 'commissionRate',
        name: 'Commission Rate',
        value: 62,
        unit: '%',
        description: 'Your commission percentage'
      }
    ],
    formula: {
      id: 'directEffortFormula',
      name: 'Direct Effort Income',
      formula: 'initialInvestment * numCases * (cdrRate / 100) * (commissionRate / 100)',
      description: 'Calculates income from direct sales efforts'
    },
    textProperties: {
      title: 'Direct Effort Income',
      subtitle: 'Income from your personal sales',
      description: 'Commission earned from your direct client acquisitions'
    },
    result: 0
  },
  recurringPac: {
    variables: [
      {
        id: 'pacPerCase',
        name: 'PAC per Case',
        value: 250,
        unit: '$',
        description: 'Average PAC contribution per client'
      },
      {
        id: 'numCases',
        name: 'Number of Cases',
        value: 5,
        unit: '',
        description: 'Number of cases with PAC'
      }
    ],
    formula: {
      id: 'recurringPacFormula',
      name: 'Recurring PAC Income',
      formula: 'pacPerCase * numCases',
      description: 'Calculates income from recurring PAC contributions'
    },
    textProperties: {
      title: 'Recurring PAC Income',
      subtitle: 'Monthly PAC contributions',
      description: 'Income from Pre-Authorized Contributions from your clients'
    },
    result: 0
  },
  trailIncome: {
    variables: [
      {
        id: 'totalAum',
        name: 'Total AUM',
        value: 1000000,
        unit: '$',
        description: 'Total client assets under your management'
      },
      {
        id: 'annualTrailRate',
        name: 'Annual Trail Rate',
        value: 0.25,
        unit: '%',
        description: 'Annual percentage paid on assets (typically 0.25%)'
      }
    ],
    formula: {
      id: 'trailIncomeFormula',
      name: 'Trail Income',
      formula: 'totalAum * (annualTrailRate / 100) / 12',
      description: 'Calculates monthly passive income from your assets under management'
    },
    textProperties: {
      title: 'Trail Income',
      subtitle: 'Passive income from AUM',
      description: 'Recurring passive income based on your assets under management'
    },
    result: 0
  },
  override: {
    variables: [
      {
        id: 'teamMembers',
        name: 'Team Members',
        value: 3,
        unit: '',
        description: 'Number of team members'
      },
      {
        id: 'avgInvestment',
        name: 'Avg. Investment',
        value: 30000,
        unit: '$',
        description: 'Average investment per case'
      },
      {
        id: 'avgCases',
        name: 'Avg. Cases',
        value: 3,
        unit: '',
        description: 'Average cases per team member'
      },
      {
        id: 'cdrRate',
        name: 'CDR Rate',
        value: 5.5,
        unit: '%',
        description: 'Commission, Dealer, Representative rate'
      },
      {
        id: 'overrideRate',
        name: 'Override Rate',
        value: 10,
        unit: '%',
        description: 'Your override percentage'
      }
    ],
    formula: {
      id: 'overrideFormula',
      name: 'Override Income',
      formula: 'teamMembers * avgInvestment * avgCases * (cdrRate / 100) * (overrideRate / 100)',
      description: 'Calculates override income from your team'
    },
    textProperties: {
      title: 'Override Income',
      subtitle: 'Income from your team',
      description: 'Commission earned from your team members\'s sales'
    },
    result: 0
  },
  agencyOverride: {
    variables: [
      {
        id: 'numAgencies',
        name: 'Number of Agencies',
        value: 3,
        unit: '',
        description: 'Number of agencies'
      },
      {
        id: 'rlsPerAgency',
        name: 'RLs per Agency',
        value: 5,
        unit: '',
        description: 'Average RLs per agency'
      },
      {
        id: 'avgInvestment',
        name: 'Avg. Investment',
        value: 35000,
        unit: '$',
        description: 'Average investment per case'
      },
      {
        id: 'avgCases',
        name: 'Avg. Cases',
        value: 4,
        unit: '',
        description: 'Average cases per RL'
      },
      {
        id: 'cdrRate',
        name: 'CDR Rate',
        value: 5.5,
        unit: '%',
        description: 'Commission, Dealer, Representative rate'
      },
      {
        id: 'agencyOverrideRate',
        name: 'Agency Override Rate',
        value: 5,
        unit: '%',
        description: 'Your agency override percentage'
      }
    ],
    formula: {
      id: 'agencyOverrideFormula',
      name: 'Agency Override Income',
      formula: 'numAgencies * rlsPerAgency * avgInvestment * avgCases * (cdrRate / 100) * (agencyOverrideRate / 100)',
      description: 'Calculates override income from your agencies'
    },
    textProperties: {
      title: 'Agency Override Income',
      subtitle: 'Income from your agencies',
      description: 'Commission earned from your agency network'
    },
    result: 0
  },
  agencyRecurringPac: {
    variables: [
      {
        id: 'numAgencies',
        name: 'Number of Agencies',
        value: 3,
        unit: '',
        description: 'Number of agencies'
      },
      {
        id: 'rlsPerAgency',
        name: 'RLs per Agency',
        value: 5,
        unit: '',
        description: 'Average RLs per agency'
      },
      {
        id: 'pacPerCase',
        name: 'PAC per Case',
        value: 250,
        unit: '$',
        description: 'Average PAC contribution per client'
      },
      {
        id: 'avgCases',
        name: 'Avg. Cases',
        value: 4,
        unit: '',
        description: 'Average cases per RL'
      },
      {
        id: 'agencyPacRate',
        name: 'Agency PAC Rate',
        value: 3,
        unit: '%',
        description: 'Your agency PAC override percentage'
      }
    ],
    formula: {
      id: 'agencyRecurringPacFormula',
      name: 'Agency Recurring PAC Income',
      formula: 'numAgencies * rlsPerAgency * pacPerCase * avgCases * (agencyPacRate / 100)',
      description: 'Calculates PAC income from your agencies'
    },
    textProperties: {
      title: 'Agency PAC Income',
      subtitle: 'PAC income from agencies',
      description: 'Recurring PAC income from your agency network'
    },
    result: 0
  },
  agencyTrail: {
    variables: [
      {
        id: 'numAgencies',
        name: 'Number of Agencies',
        value: 3,
        unit: '',
        description: 'Number of agencies'
      },
      {
        id: 'rlsPerAgency',
        name: 'RLs per Agency',
        value: 5,
        unit: '',
        description: 'Average RLs per agency'
      },
      {
        id: 'avgAumPerRl',
        name: 'Avg. AUM per RL',
        value: 500000,
        unit: '$',
        description: 'Average AUM per RL'
      },
      {
        id: 'annualTrailRate',
        name: 'Annual Trail Rate',
        value: 0.25,
        unit: '%',
        description: 'Annual trail percentage'
      },
      {
        id: 'agencyTrailRate',
        name: 'Agency Trail Rate',
        value: 3,
        unit: '%',
        description: 'Your agency trail override percentage'
      }
    ],
    formula: {
      id: 'agencyTrailFormula',
      name: 'Agency Trail Income',
      formula: 'numAgencies * rlsPerAgency * avgAumPerRl * (annualTrailRate / 100) / 12 * (agencyTrailRate / 100)',
      description: 'Calculates trail income from your agencies'
    },
    textProperties: {
      title: 'Agency Trail Income',
      subtitle: 'Trail income from agencies',
      description: 'Passive trail income from your agency network'
    },
    result: 0
  }
};

// Calculate projected AUM based on simulator inputs
const calculateProjectedAUM = (simulatorInputs: SimpleSimulationInputs): number => {
  if (!simulatorInputs) return 1000000; // Default fallback

  const {
    initialInvestment = 25000,
    numRolloverCases = 5,
    pacPerCase = 250,
    numPacCases = 5,
    projectionYears = 10,
    marketGrowth = 0.10
  } = simulatorInputs;

  // Calculate initial investment total
  const totalInitialInvestment = initialInvestment * numRolloverCases;

  // Calculate monthly PAC contribution
  const monthlyPacContribution = pacPerCase * numPacCases;

  // Use the trailsLogic function to calculate projected AUM with contributions
  const projectedAUM = trailsLogic.calculateProjectedAUMWithContributions(
    totalInitialInvestment,
    monthlyPacContribution,
    projectionYears,
    marketGrowth
  );

  return projectedAUM;
};

// Safe math expression evaluator
const safeEvaluate = (expression: string): number => {
  try {
    // Remove whitespace
    const cleanExpr = expression.replace(/\s/g, '');

    // Only allow numbers, operators, parentheses, and decimal points
    if (!/^[0-9+\-*/().]+$/.test(cleanExpr)) {
      throw new Error('Invalid characters in expression');
    }

    // Use Function constructor for safer evaluation than eval
    // This still evaluates code but is more restricted than eval
    const result = new Function(`"use strict"; return (${cleanExpr})`)();

    if (typeof result !== 'number' || !isFinite(result)) {
      throw new Error('Invalid calculation result');
    }

    return result;
  } catch (error) {
    console.error('Error in safe evaluation:', error);
    return 0;
  }
};

// Calculate result for a calculation set
const calculateResult = (calculationSet: CalculationSet, simulatorInputs?: SimpleSimulationInputs): number => {
  try {
    // Create a scope with all variables
    const scope: Record<string, number> = {};
    calculationSet.variables.forEach(v => {
      scope[v.id] = v.value;
    });

    // For trail calculations, replace static AUM with calculated AUM
    if (calculationSet.formula.id === 'trailIncomeFormula' && simulatorInputs) {
      const projectedAUM = calculateProjectedAUM(simulatorInputs);
      scope['totalAum'] = projectedAUM;
    }

    // For agency trail calculations, calculate AUM per RL based on simulator inputs
    if (calculationSet.formula.id === 'agencyTrailFormula' && simulatorInputs) {
      const projectedAUM = calculateProjectedAUM(simulatorInputs);
      // Each RL produces at 50% of the main production
      const aumPerRL = projectedAUM * 0.5;
      scope['avgAumPerRl'] = aumPerRL;
    }

    // Replace variable IDs with their values in the formula
    let evalFormula = calculationSet.formula.formula;
    for (const varId in scope) {
      evalFormula = evalFormula.replace(new RegExp(varId, 'g'), scope[varId].toString());
    }

    // Use safe evaluation instead of eval
    return safeEvaluate(evalFormula);
  } catch (error) {
    console.error('Error calculating result:', error);
    return 0;
  }
};

// Hook for managing calculations
export const useCalculations = (initialCalculations?: Partial<Calculations>, simulatorInputs?: SimpleSimulationInputs) => {
  const [calculations, setCalculations] = useState<Calculations>(() => {
    // If simulator inputs are provided, update default values to match
    const updatedDefaults = { ...defaultCalculations };

    if (simulatorInputs) {
      // Update Direct Effort defaults
      updatedDefaults.directEffort.variables = updatedDefaults.directEffort.variables.map(v => {
        if (v.id === 'initialInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'numCases') return { ...v, value: simulatorInputs.numRolloverCases || 5 };
        return v;
      });

      // Update Recurring PAC defaults
      updatedDefaults.recurringPac.variables = updatedDefaults.recurringPac.variables.map(v => {
        if (v.id === 'pacPerCase') return { ...v, value: simulatorInputs.pacPerCase || 250 };
        if (v.id === 'numCases') return { ...v, value: simulatorInputs.numPacCases || 5 };
        return v;
      });

      // Update Override defaults (RLs produce at 50% of RVP level)
      updatedDefaults.override.variables = updatedDefaults.override.variables.map(v => {
        if (v.id === 'teamMembers') return { ...v, value: simulatorInputs.numRLs || 3 };
        if (v.id === 'avgInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numRolloverCases || 5) * 0.5) }; // 50% of RVP cases
        return v;
      });

      // Update Agency Override defaults (RLs produce at 50% of RVP level)
      updatedDefaults.agencyOverride.variables = updatedDefaults.agencyOverride.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        if (v.id === 'avgInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numRolloverCases || 5) * 0.5) }; // 50% of RVP cases
        return v;
      });

      // Update Agency Recurring PAC defaults (RLs produce at 50% of RVP level)
      updatedDefaults.agencyRecurringPac.variables = updatedDefaults.agencyRecurringPac.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        if (v.id === 'pacPerCase') return { ...v, value: simulatorInputs.pacPerCase || 250 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numPacCases || 5) * 0.5) }; // 50% of RVP PAC cases
        return v;
      });

      // Update Agency Trail defaults
      updatedDefaults.agencyTrail.variables = updatedDefaults.agencyTrail.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        return v;
      });
    }

    return {
      ...updatedDefaults,
      ...initialCalculations
    };
  });

  // Memoize calculated results to avoid unnecessary recalculations
  const calculatedResults = useMemo(() => {
    const updatedCalculations = { ...calculations };

    // Calculate each result
    for (const key in updatedCalculations) {
      const calcKey = key as keyof Calculations;
      updatedCalculations[calcKey].result = calculateResult(updatedCalculations[calcKey], simulatorInputs);
    }

    return updatedCalculations;
  }, [calculations, simulatorInputs]);

  // Update calculations when calculated results change
  useEffect(() => {
    setCalculations(calculatedResults);
  }, [calculatedResults]);

  // Update a specific calculation set
  const updateCalculation = useCallback((
    incomeType: IncomeType,
    updates: {
      variables?: CalculationVariable[];
      formula?: CalculationFormula;
      textProperties?: CardTextProperties;
    }
  ) => {
    setCalculations(prev => {
      const updated = {
        ...prev,
        [incomeType]: {
          variables: updates.variables || prev[incomeType].variables,
          formula: updates.formula || prev[incomeType].formula,
          result: 0, // Will be recalculated below
          textProperties: updates.textProperties || prev[incomeType].textProperties
        }
      };

      // Calculate the new result
      updated[incomeType].result = calculateResult(updated[incomeType], simulatorInputs);

      return updated;
    });
  }, [simulatorInputs]);

  // Update calculations when simulator inputs change
  const updateFromSimulatorInputs = useCallback((simulatorInputs: SimpleSimulationInputs) => {
    setCalculations(prev => {
      const updated = { ...prev };

      // Update Direct Effort
      updated.directEffort.variables = updated.directEffort.variables.map(v => {
        if (v.id === 'initialInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'numCases') return { ...v, value: simulatorInputs.numRolloverCases || 5 };
        return v;
      });

      // Update Recurring PAC
      updated.recurringPac.variables = updated.recurringPac.variables.map(v => {
        if (v.id === 'pacPerCase') return { ...v, value: simulatorInputs.pacPerCase || 250 };
        if (v.id === 'numCases') return { ...v, value: simulatorInputs.numPacCases || 5 };
        return v;
      });

      // Update Override (RLs produce at 50% of RVP level)
      updated.override.variables = updated.override.variables.map(v => {
        if (v.id === 'teamMembers') return { ...v, value: simulatorInputs.numRLs || 3 };
        if (v.id === 'avgInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numRolloverCases || 5) * 0.5) }; // 50% of RVP cases
        return v;
      });

      // Update Agency Override (RLs produce at 50% of RVP level)
      updated.agencyOverride.variables = updated.agencyOverride.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        if (v.id === 'avgInvestment') return { ...v, value: simulatorInputs.initialInvestment || 25000 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numRolloverCases || 5) * 0.5) }; // 50% of RVP cases
        return v;
      });

      // Update Agency Recurring PAC (RLs produce at 50% of RVP level)
      updated.agencyRecurringPac.variables = updated.agencyRecurringPac.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        if (v.id === 'pacPerCase') return { ...v, value: simulatorInputs.pacPerCase || 250 };
        if (v.id === 'avgCases') return { ...v, value: Math.round((simulatorInputs.numPacCases || 5) * 0.5) }; // 50% of RVP PAC cases
        return v;
      });

      // Update Agency Trail
      updated.agencyTrail.variables = updated.agencyTrail.variables.map(v => {
        if (v.id === 'numAgencies') return { ...v, value: simulatorInputs.numAgencies || 2 };
        if (v.id === 'rlsPerAgency') return { ...v, value: simulatorInputs.numRLsPerAgency || 3 };
        return v;
      });

      // Recalculate all results
      for (const key in updated) {
        const calcKey = key as keyof Calculations;
        updated[calcKey].result = calculateResult(updated[calcKey], simulatorInputs);
      }

      return updated;
    });
  }, []);

  // Reset all calculations to default
  const resetCalculations = useCallback(() => {
    setCalculations(defaultCalculations);
  }, []);

  // Get total income
  const getTotalIncome = useCallback((): number => {
    return Object.values(calculations).reduce((sum, calc) => sum + calc.result, 0);
  }, [calculations]);

  return {
    calculations,
    updateCalculation,
    updateFromSimulatorInputs,
    resetCalculations,
    getTotalIncome
  };
};
