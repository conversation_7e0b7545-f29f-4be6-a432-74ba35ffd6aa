
import { SimpleSimulationInputs, SimpleScenarioResults } from '@/types/simpleTypes';
import { trailsLogic } from '../simConfig/trailsLogic';
import { measurePerformance } from './performance';

// Constants for commission rates
const RVP_BP = 0.62; // 62% for RVP (Business Owner)
const RL_BP = 0.425; // 42.5% for RL (Self Employed)
const OVERRIDE_SPREAD = 0.195; // 19.5% override difference
const EQUITY_TRAIL_RATE = 0.0025; // 0.25% trail rate for equity funds
const TEAM_PRODUCTION_RATIO = 0.5; // Team members produce at 50% of upline

export function calculateSimpleScenarios(inputs: SimpleSimulationInputs): SimpleScenarioResults {
  return measurePerformance('calculateSimpleScenarios', () => {
  const {
    initialInvestment,
    numRolloverCases,
    pacPerCase,
    numPacCases,
    projectionYears,
    marketGrowth,
    numRLs,
    numAgencies,
    numRLsPerAgency
  } = inputs;

  // Convert annual growth to monthly
  const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;

  // Calculate months of projection
  const projectionMonths = projectionYears * 12;

  // Calculate initial investments and monthly contributions
  const totalInitialInvestment = initialInvestment * numRolloverCases;
  const monthlyPacContribution = pacPerCase * numPacCases;

  // Calculate total AUM based on rollover + PAC contributions over time with growth
  let personalAum = totalInitialInvestment;
  let pacContributions = 0;

  // Compound growth for existing investments and add new PAC contributions each month
  for (let month = 0; month < projectionMonths; month++) {
    // Add PAC contribution for the month
    pacContributions += monthlyPacContribution;

    // Grow existing AUM
    personalAum = (personalAum + monthlyPacContribution) * (1 + monthlyGrowth);
  }

  // Calculate direct commission income (one-time) for self-employed
  const selfEmployedDirectIncome = totalInitialInvestment * 0.055 * RL_BP; // Using 5.5% CDR rate for simplicity

  // Calculate recurring PAC income for self-employed (only this month's PAC)
  const selfEmployedPacIncome = monthlyPacContribution * 0.055 * RL_BP;

  // Calculate trail income based on accumulated AUM for self-employed
  const selfEmployedTrailIncome = personalAum * EQUITY_TRAIL_RATE * RL_BP / 12; // Monthly trail

  // Calculate AUM and income for team members (RL production at 50% of SE)
  const teamMemberProduction = TEAM_PRODUCTION_RATIO;
  const teamInitialInvestment = totalInitialInvestment * teamMemberProduction * numRLs;
  const teamMonthlyPac = monthlyPacContribution * teamMemberProduction * numRLs;

  // Calculate team AUM
  let teamAum = teamInitialInvestment;
  let teamPacContributions = 0;

  for (let month = 0; month < projectionMonths; month++) {
    teamPacContributions += teamMonthlyPac;
    teamAum = (teamAum + teamMonthlyPac) * (1 + monthlyGrowth);
  }

  // Calculate override income for SE+Team (RVP with team of RLs)
  const teamOverride = (teamInitialInvestment * 0.055 * OVERRIDE_SPREAD) +
                       (teamMonthlyPac * 0.055 * OVERRIDE_SPREAD);

  // Calculate RVP direct income (for business owner)
  const rvpDirectIncome = totalInitialInvestment * 0.055 * RVP_BP;
  const rvpPacIncome = monthlyPacContribution * 0.055 * RVP_BP;
  const rvpTrailIncome = personalAum * EQUITY_TRAIL_RATE * RVP_BP / 12;

  // Calculate income from personal team of RLs for business owner
  const personalTeamOverride = teamOverride;

  // Calculate agency production (each agency has RVPs with their own teams)
  const agencyInitialInvestment = teamInitialInvestment * numAgencies;
  const agencyMonthlyPac = teamMonthlyPac * numAgencies;

  // Calculate agency AUM
  let agencyAum = agencyInitialInvestment;
  let agencyPacContributions = 0;

  for (let month = 0; month < projectionMonths; month++) {
    agencyPacContributions += agencyMonthlyPac;
    agencyAum = (agencyAum + agencyMonthlyPac) * (1 + monthlyGrowth);
  }

  // Calculate first-gen override for business owner (from agencies)
  const firstGenOverrideRate = 0.061; // 6.1% first generation override
  const agencyOverrideIncome = (agencyInitialInvestment * 0.055 * firstGenOverrideRate) +
                              (agencyMonthlyPac * 0.055 * firstGenOverrideRate);

  // Calculate trail income from agencies
  const agencyTrailIncome = agencyAum * EQUITY_TRAIL_RATE * 0.075 / 12; // Using 7.5% trail override

  // Calculate recurring PAC income from agencies (just this month's PAC)
  const agencyRecurringPacIncome = agencyMonthlyPac * 0.055 * 0.061;

  // Build the result object
  const results: SimpleScenarioResults = {
    selfEmployed: {
      directEffort: selfEmployedDirectIncome,
      recurringPac: selfEmployedPacIncome,
      trailIncome: selfEmployedTrailIncome,
      total: selfEmployedDirectIncome + selfEmployedPacIncome + selfEmployedTrailIncome
    },
    selfEmployedTeam: {
      directEffort: rvpDirectIncome,
      teamOverride: teamOverride,
      recurringPac: rvpPacIncome,
      trailIncome: rvpTrailIncome + teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12,
      total: rvpDirectIncome + teamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12)
    },
    businessOwner: {
      personalOffice: {
        directEffort: rvpDirectIncome,
        override: personalTeamOverride,
        recurringPac: rvpPacIncome,
        trailIncome: rvpTrailIncome + (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12),
        total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
               (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12)
      },
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome,
        recurringPacIncome: agencyRecurringPacIncome,
        trailIncome: agencyTrailIncome,
        total: agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
      },
      total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12) +
             agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
    },
    passiveOwner: {
      // For passive owner, calculate recurring PAC income based on accumulated clients over years
      // Assume 3x the monthly PAC of a regular business owner to represent accumulated clients
      recurringPac: rvpPacIncome * 3,
      // Trail income is higher due to accumulated assets over years
      trailIncome: rvpTrailIncome * 2.5,
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome * 1.25, // Slightly higher for passive owner who focuses on expansion
        recurringPacIncome: agencyRecurringPacIncome * 1.25,
        trailIncome: agencyTrailIncome * 1.25,
        total: (agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome) * 1.25
      },
      total: rvpPacIncome * 3 + rvpTrailIncome * 2.5 + (agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome) * 1.25
    },
    aum: {
      personal: personalAum,
      team: teamAum,
      agencies: agencyAum,
      total: personalAum + teamAum + agencyAum
    }
  };

  return results;
  });
}
