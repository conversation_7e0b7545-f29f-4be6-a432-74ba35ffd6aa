
import { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate } from 'react-router-dom';
import CompEdgeHeader from '@/components/CompEdgeHeader';
import SettingsPanel from '@/components/SettingsPanel';
import { motion } from 'framer-motion';

// Import our new components
import InputPanel from '@/components/simulator/inputs/InputPanel';
import ScenarioCardGrid from '@/components/simulator/simple/ScenarioCardGrid';

// Import utility functions
import { calculateSimpleScenarios } from '@/utils/simpleCalculator';

const SimpleIncome = () => {
  const navigate = useNavigate();

  // Default inputs for simple income simulator
  const [inputs, setInputs] = useState({
    initialInvestment: 25000,
    numRolloverCases: 5,
    pacPerCase: 250,
    numPacCases: 5,
    projectionYears: 10,
    marketGrowth: 0.10, // 10% annual growth
    numRLs: 3,
    numAgencies: 2,
    numRLsPerAgency: 3,
    aum: 1000000, // $1M AUM for trail income
    trailRate: 0.0025 // 0.25% annual trail rate
  });

  // Store calculated results
  const [results, setResults] = useState(null);

  // Memoize calculated results to avoid unnecessary recalculations
  const calculatedResults = useMemo(() => {
    return calculateSimpleScenarios({
      initialInvestment: inputs.initialInvestment,
      numRolloverCases: inputs.numRolloverCases,
      pacPerCase: inputs.pacPerCase,
      numPacCases: inputs.numPacCases,
      projectionYears: inputs.projectionYears,
      marketGrowth: inputs.marketGrowth,
      numRLs: inputs.numRLs,
      numAgencies: inputs.numAgencies,
      numRLsPerAgency: inputs.numRLsPerAgency
    });
  }, [inputs]);

  // Update results when calculated results change
  const calculateScenarios = useCallback(() => {
    setResults(calculatedResults);
  }, [calculatedResults]);

  // Handle input changes
  const handleInputChange = (name: string, value: number) => {
    setInputs(prev => ({...prev, [name]: value}));
  };

  // Calculate scenarios when inputs change
  useEffect(() => {
    calculateScenarios();
  }, [calculateScenarios]);

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      <CompEdgeHeader />
      <SettingsPanel />

      <ScrollArea className="flex-1">
        <motion.main
          className="container py-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-white">
            <div className="p-6">
              <Tabs defaultValue="simple" onValueChange={(value) => {
                if (value === "advanced") navigate("/advanced");
              }}>
                <TabsList className="grid w-full grid-cols-2 mb-8">
                  <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                </TabsList>

                <TabsContent value="simple" className="space-y-8">
                  <InputPanel
                    inputs={inputs}
                    onChange={handleInputChange}
                    onCalculate={calculateScenarios}
                  />

                  {results && (
                    <ScenarioCardGrid
                      results={results}
                      inputs={inputs}
                      onInputChange={handleInputChange}
                    />
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </Card>
        </motion.main>
      </ScrollArea>
    </div>
  );
};

export default SimpleIncome;
